// ===== PRELOADER =====
window.addEventListener('load', function() {
    const preloader = document.getElementById('preloader');
    const loadingProgress = document.querySelector('.loading-progress');

    // Animate loading bar
    let progress = 0;
    const loadingInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;
        loadingProgress.style.width = progress + '%';

        if (progress >= 100) {
            clearInterval(loadingInterval);
            setTimeout(() => {
                preloader.classList.add('hidden');
                setTimeout(() => {
                    preloader.style.display = 'none';
                    // Initialize AOS after preloader
                    AOS.init({
                        duration: 1000,
                        once: true,
                        offset: 100
                    });
                }, 800);
            }, 500);
        }
    }, 100);
});

// ===== NAVIGATION =====
const navbar = document.getElementById('navbar');
const hamburger = document.getElementById('hamburger');
const navMenu = document.getElementById('nav-menu');
const navLinks = document.querySelectorAll('.nav-link');
const themeToggle = document.getElementById('theme-toggle');

// Navbar scroll effect
window.addEventListener('scroll', function() {
    if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});

// Theme toggle
let isDarkTheme = false;
themeToggle.addEventListener('click', function() {
    isDarkTheme = !isDarkTheme;
    document.body.classList.toggle('dark-theme', isDarkTheme);

    const icon = this.querySelector('i');
    if (isDarkTheme) {
        icon.className = 'fas fa-sun';
    } else {
        icon.className = 'fas fa-moon';
    }

    // Store theme preference
    localStorage.setItem('darkTheme', isDarkTheme);
});

// Load saved theme
const savedTheme = localStorage.getItem('darkTheme');
if (savedTheme === 'true') {
    isDarkTheme = true;
    document.body.classList.add('dark-theme');
    themeToggle.querySelector('i').className = 'fas fa-sun';
}

// Mobile menu toggle
hamburger.addEventListener('click', function() {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
    document.body.classList.toggle('menu-open');
});

// Close mobile menu when clicking on a link
navLinks.forEach(link => {
    link.addEventListener('click', function() {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
        document.body.classList.remove('menu-open');
    });
});

// Active navigation link with smooth highlighting
function updateActiveNavLink() {
    const sections = document.querySelectorAll('section');
    const scrollPos = window.scrollY + 150;

    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${sectionId}`) {
                    link.classList.add('active');
                }
            });
        }
    });
}

window.addEventListener('scroll', throttle(updateActiveNavLink, 16));

// ===== SMOOTH SCROLLING =====
navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const targetSection = document.querySelector(targetId);

        if (targetSection) {
            const offsetTop = targetSection.offsetTop - 80;
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
    });
});

// ===== ENHANCED ANIMATED COUNTERS =====
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;

        const updateCounter = () => {
            if (current < target) {
                current += increment;
                if (target > 1000) {
                    counter.textContent = Math.ceil(current).toLocaleString();
                } else {
                    counter.textContent = Math.ceil(current);
                }
                requestAnimationFrame(updateCounter);
            } else {
                if (target > 1000) {
                    counter.textContent = target.toLocaleString();
                } else {
                    counter.textContent = target;
                }
            }
        };

        updateCounter();
    });
}

// ===== GENERATION CIRCLES INTERACTION =====
function initGenerationCircles() {
    const genCircles = document.querySelectorAll('.gen-circle');

    genCircles.forEach(circle => {
        circle.addEventListener('mouseenter', function() {
            // Pause other animations
            genCircles.forEach(otherCircle => {
                if (otherCircle !== this) {
                    otherCircle.style.opacity = '0.5';
                }
            });

            // Highlight connections
            const connectionLines = document.querySelectorAll('.connection-line');
            connectionLines.forEach(line => {
                line.style.stroke = 'rgba(245, 158, 11, 0.8)';
                line.style.strokeWidth = '3';
            });
        });

        circle.addEventListener('mouseleave', function() {
            // Restore all circles
            genCircles.forEach(otherCircle => {
                otherCircle.style.opacity = '1';
            });

            // Reset connections
            const connectionLines = document.querySelectorAll('.connection-line');
            connectionLines.forEach(line => {
                line.style.stroke = 'rgba(255, 255, 255, 0.2)';
                line.style.strokeWidth = '2';
            });
        });
    });
}

// ===== PHILOSOPHY MODEL INTERACTION =====
function initPhilosophyModel() {
    const modelElements = document.querySelectorAll('.model-element');
    const centerCircle = document.querySelector('.center-circle');

    modelElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            // Highlight center
            centerCircle.style.transform = 'scale(1.1)';
            centerCircle.style.boxShadow = '0 0 50px rgba(245, 158, 11, 0.8)';

            // Create connection effect
            const connections = document.querySelectorAll('.connection-path');
            connections.forEach(path => {
                path.style.stroke = 'var(--accent-color)';
                path.style.strokeWidth = '3';
                path.style.opacity = '1';
            });
        });

        element.addEventListener('mouseleave', function() {
            // Reset center
            centerCircle.style.transform = 'scale(1)';
            centerCircle.style.boxShadow = 'var(--shadow-glow-accent)';

            // Reset connections
            const connections = document.querySelectorAll('.connection-path');
            connections.forEach(path => {
                path.style.stroke = 'var(--accent-color)';
                path.style.strokeWidth = '2';
                path.style.opacity = '0.6';
            });
        });
    });
}

// ===== ENHANCED INTERSECTION OBSERVER =====
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -100px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('visible');

            // Trigger counter animation for stats
            if (entry.target.classList.contains('hero-stats') ||
                entry.target.classList.contains('stats-container')) {
                animateCounters();
            }

            // Trigger timeline animations
            if (entry.target.classList.contains('timeline-item')) {
                entry.target.style.animationDelay = '0s';
                entry.target.style.animation = 'slideInFromSide 0.8s ease forwards';
            }

            // Trigger value card animations
            if (entry.target.classList.contains('value-card')) {
                const delay = Array.from(entry.target.parentNode.children).indexOf(entry.target) * 0.2;
                entry.target.style.animationDelay = delay + 's';
                entry.target.style.animation = 'fadeInUp 0.8s ease forwards';
            }

            // Trigger principle card animations
            if (entry.target.classList.contains('principle-card')) {
                const delay = Array.from(entry.target.parentNode.children).indexOf(entry.target) * 0.3;
                entry.target.style.animationDelay = delay + 's';
                entry.target.style.animation = 'slideInLeft 0.8s ease forwards';
            }
        }
    });
}, observerOptions);

// ===== PARALLAX EFFECTS =====
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.hero-background, .philosophy-particles');

    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;

        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    });
}

// ===== FLOATING SHAPES ANIMATION =====
function initFloatingShapes() {
    const shapes = document.querySelectorAll('.floating-shape');

    shapes.forEach((shape) => {
        const randomDelay = Math.random() * 5;
        const randomDuration = 15 + Math.random() * 10;

        shape.style.animationDelay = randomDelay + 's';
        shape.style.animationDuration = randomDuration + 's';

        // Add mouse interaction
        shape.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
            this.style.transform = 'scale(1.2)';
        });

        shape.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
            this.style.transform = 'scale(1)';
        });
    });
}

// ===== ADVANCED HOVER EFFECTS =====
function initAdvancedHoverEffects() {
    // Card tilt effect
    const cards = document.querySelectorAll('.value-card, .principle-card, .timeline-content');

    cards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
        });
    });

    // Magnetic effect for buttons
    const buttons = document.querySelectorAll('.btn');

    buttons.forEach(button => {
        button.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            this.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translate(0px, 0px)';
        });
    });
}

// ===== SCROLL PROGRESS INDICATOR =====
function initScrollProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        z-index: 9999;
        transition: width 0.1s ease;
    `;
    document.body.appendChild(progressBar);

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        progressBar.style.width = scrollPercent + '%';
    });
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all interactive features
    initGenerationCircles();
    initPhilosophyModel();
    initParallaxEffects();
    initFloatingShapes();
    initAdvancedHoverEffects();
    initScrollProgress();

    // Observe elements for animations
    const animatedElements = document.querySelectorAll(
        '.hero-stats, .stats-container, .timeline-item, .value-card, .principle-card, .impact-stat'
    );
    animatedElements.forEach(el => observer.observe(el));

    // Add custom animations
    addCustomAnimations();
});

// ===== CUSTOM ANIMATIONS =====
function addCustomAnimations() {
    // Add CSS animations dynamically
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInFromSide {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// ===== ENHANCED INTERACTIONS =====
function initEnhancedInteractions() {
    // Trust logos hover effect
    const trustLogos = document.querySelectorAll('.trust-logo');
    trustLogos.forEach(logo => {
        logo.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.05)';
            this.style.background = 'rgba(245, 158, 11, 0.2)';
        });

        logo.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.background = 'rgba(255, 255, 255, 0.1)';
        });
    });

    // Timeline item interactions
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const year = this.querySelector('.timeline-year');
            year.style.transform = 'scale(1.1) rotate(5deg)';
            year.style.boxShadow = '0 0 30px rgba(99, 102, 241, 0.5)';
        });

        item.addEventListener('mouseleave', function() {
            const year = this.querySelector('.timeline-year');
            year.style.transform = 'scale(1) rotate(0deg)';
            year.style.boxShadow = 'var(--shadow-xl)';
        });
    });
}

// ===== PERFORMANCE OPTIMIZATIONS =====
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ===== RESPONSIVE UTILITIES =====
function initResponsiveFeatures() {
    // Mobile menu improvements
    const navMenu = document.getElementById('nav-menu');

    // Close menu on outside click
    document.addEventListener('click', function(e) {
        if (!navMenu.contains(e.target) && !hamburger.contains(e.target)) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.classList.remove('menu-open');
        }
    });

    // Handle window resize
    window.addEventListener('resize', throttle(() => {
        // Reset mobile menu on desktop
        if (window.innerWidth > 768) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.classList.remove('menu-open');
        }
    }, 250));
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
function initAccessibilityFeatures() {
    // Keyboard navigation for interactive elements
    const interactiveElements = document.querySelectorAll('.btn, .nav-link, .gen-circle, .model-element');

    interactiveElements.forEach(element => {
        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });

    // Focus management
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });

    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
}

// ===== FINAL INITIALIZATION =====
window.addEventListener('load', function() {
    initEnhancedInteractions();
    initResponsiveFeatures();
    initAccessibilityFeatures();

    // Add loading complete class
    document.body.classList.add('loaded');
});
/* ===== MODERN CSS RESET & VARIABLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Color Palette */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #8b5cf6;
    --accent-color: #f59e0b;
    --accent-dark: #d97706;
    --accent-light: #fbbf24;
    --secondary-color: #06b6d4;
    --secondary-dark: #0891b2;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* Neutral Colors */
    --dark-color: #0f172a;
    --dark-secondary: #1e293b;
    --light-color: #f8fafc;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Typography */
    --font-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Merriweather', Georgia, serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

    /* Spacing System */
    --container-max-width: 1400px;
    --container-padding: 2rem;
    --section-padding: 120px 0;
    --element-spacing: 2rem;

    /* Animation & Transitions */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Shadows & Effects */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    --shadow-glow-accent: 0 0 30px rgba(245, 158, 11, 0.4);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
    --gradient-dark: linear-gradient(135deg, var(--dark-color) 0%, var(--dark-secondary) 100%);
    --gradient-rainbow: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

/* ===== BASE STYLES ===== */
html {
    scroll-behavior: smooth;
    font-size: 16px;
    overflow-x: hidden;
}

body {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.7;
    color: var(--gray-700);
    background: var(--white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body.dark-theme {
    background: var(--dark-color);
    color: var(--gray-200);
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

h1 { font-size: clamp(2.5rem, 5vw, 4.5rem); }
h2 { font-size: clamp(2rem, 4vw, 3.5rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2.5rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 2rem); }
h5 { font-size: clamp(1.125rem, 2vw, 1.5rem); }
h6 { font-size: clamp(1rem, 1.5vw, 1.25rem); }

p {
    margin-bottom: 1.5rem;
    color: var(--gray-600);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
}

.accent-text {
    color: var(--accent-color);
}

/* ===== FLOATING ELEMENTS ===== */
.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-shape {
    position: absolute;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    opacity: 0.1;
    animation: float 20s infinite ease-in-out;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: -10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: -5%;
    animation-delay: -5s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    top: 30%;
    left: 70%;
    animation-delay: -10s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 80%;
    left: 20%;
    animation-delay: -15s;
}

.shape-5 {
    width: 250px;
    height: 250px;
    top: 40%;
    right: 30%;
    animation-delay: -7s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-30px) rotate(120deg);
    }
    66% {
        transform: translateY(30px) rotate(240deg);
    }
}

/* ===== PRELOADER ===== */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.8s ease, visibility 0.8s ease;
}

#preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    text-align: center;
    color: var(--white);
}

.preloader-logo {
    margin-bottom: 2rem;
}

.logo-circle {
    width: 120px;
    height: 120px;
    border: 3px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    animation: pulse-ring 2s infinite;
}

.logo-circle::before {
    content: '';
    position: absolute;
    width: 140px;
    height: 140px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    animation: pulse-ring 2s infinite 0.5s;
}

.logo-inner {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: rotate 3s linear infinite;
}

.logo-inner i {
    font-size: 2rem;
    color: var(--white);
}

.preloader-text h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--white);
}

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    margin: 0 auto 1rem;
    overflow: hidden;
}

.loading-progress {
    height: 100%;
    background: var(--gradient-accent);
    border-radius: var(--radius-full);
    animation: loading 3s ease-in-out infinite;
}

.preloader-text p {
    font-size: 0.9rem;
    opacity: 0.8;
    color: var(--white);
}

@keyframes pulse-ring {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes loading {
    0% {
        width: 0%;
        transform: translateX(-100%);
    }
    50% {
        width: 100%;
        transform: translateX(0%);
    }
    100% {
        width: 100%;
        transform: translateX(100%);
    }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    transition: all var(--transition-normal);
    padding: 0.5rem 0;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
    padding: 0.25rem 0;
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.navbar.scrolled .nav-container {
    height: 70px;
}

.logo-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.logo-icon:hover {
    transform: rotate(10deg) scale(1.1);
    box-shadow: var(--shadow-glow);
}

.logo-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.logo-text h2 {
    font-family: var(--font-heading);
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--dark-color);
    margin: 0;
}

.logo-text .accent {
    color: var(--accent-color);
}

.logo-tagline {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0.5rem;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    font-size: 0.9rem;
    border-radius: var(--radius-lg);
    position: relative;
    transition: all var(--transition-normal);
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left var(--transition-normal);
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-link i {
    font-size: 0.9rem;
    transition: transform var(--transition-normal);
}

.nav-link:hover i {
    transform: scale(1.2);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    width: 45px;
    height: 45px;
    background: var(--gray-100);
    border: none;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--gray-600);
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: rotate(180deg);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
    padding: 0.5rem;
}

.hamburger .bar {
    width: 25px;
    height: 3px;
    background: var(--gray-700);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
}

.hamburger.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active .bar:nth-child(2) {
    opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== HERO SECTION ===== */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: var(--gradient-dark);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(15, 23, 42, 0.8) 0%,
        rgba(30, 41, 59, 0.6) 50%,
        rgba(99, 102, 241, 0.3) 100%);
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    animation: particles 20s linear infinite;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        transparent 0%,
        rgba(99, 102, 241, 0.1) 25%,
        transparent 50%,
        rgba(245, 158, 11, 0.1) 75%,
        transparent 100%);
    animation: gradient-shift 15s ease-in-out infinite;
}

@keyframes particles {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

@keyframes gradient-shift {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

.hero-content {
    position: relative;
    z-index: 1;
    width: 100%;
    padding: 120px 0 80px;
}

.hero-main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 6rem;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-full);
    color: var(--white);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 2rem;
    transition: all var(--transition-normal);
}

.hero-badge:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.hero-badge i {
    color: var(--accent-color);
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    color: var(--white);
    margin-bottom: 2rem;
}

.title-word {
    display: inline-block;
    margin-right: 0.5rem;
    opacity: 0;
    transform: translateY(50px);
    animation: slideInUp 0.8s ease forwards;
}

.title-word:nth-child(2) { animation-delay: 0.2s; }
.title-word:nth-child(3) { animation-delay: 0.4s; }
.title-word:nth-child(4) { animation-delay: 0.6s; }

.hero-subtitle {
    margin-bottom: 3rem;
}

.subtitle-main {
    font-size: 1.25rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.subtitle-highlights {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

.highlight-item i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn-large {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-accent);
    color: var(--white);
    box-shadow: var(--shadow-glow-accent);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-2xl), var(--shadow-glow-accent);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-color);
    transform: translateY(-3px);
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero Visual Elements */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.visual-container {
    position: relative;
    width: 400px;
    height: 400px;
}

.generation-circles {
    position: relative;
    width: 100%;
    height: 100%;
}

.gen-circle {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-bounce);
    cursor: pointer;
}

.gen-circle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

.gen-z {
    top: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(99, 102, 241, 0.3));
}

.millennials {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(6, 182, 212, 0.3));
}

.gen-x {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(16, 185, 129, 0.3));
}

.boomers {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.3), rgba(251, 191, 36, 0.3));
}

.circle-content {
    text-align: center;
    color: var(--white);
}

.circle-content i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.circle-content span {
    font-weight: 600;
    font-size: 0.9rem;
    display: block;
}

.circle-content small {
    font-size: 0.7rem;
    opacity: 0.8;
}

.connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.connection-line {
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 2;
    fill: none;
    stroke-dasharray: 5, 5;
    animation: dash 3s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: -20;
    }
}

/* Hero Trust Section */
.hero-trust {
    text-align: center;
}

.trust-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.trust-logos {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.trust-logo {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
    font-weight: 500;
    transition: all var(--transition-normal);
}

.trust-logo:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Hero Stats */
.hero-stats {
    margin-top: 4rem;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: 3rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    text-align: left;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--white);
    font-family: var(--font-heading);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 0.25rem;
}

.stat-description {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
}

/* Hero Scroll Indicator */
.hero-scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.scroll-mouse {
    width: 30px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    margin: 0 auto 1rem;
    position: relative;
}

.scroll-wheel {
    width: 4px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 2px;
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    animation: scroll-wheel 2s infinite;
}

@keyframes scroll-wheel {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
}

.hero-scroll-indicator p {
    font-size: 0.85rem;
    margin: 0;
}

/* ===== SECTION STYLES ===== */
section {
    padding: var(--section-padding);
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-primary);
    color: var(--white);
    padding: 0.5rem 1.5rem;
    border-radius: var(--radius-full);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-glow);
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.2;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

.section-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    color: var(--gray-600);
    max-width: 700px;
    margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about-section {
    background: var(--gray-50);
    position: relative;
    overflow: hidden;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(99,102,241,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.about-content {
    position: relative;
    z-index: 1;
}

.about-story {
    margin-bottom: 6rem;
}

.story-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.story-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-primary);
    transform: translateX(-50%);
    border-radius: var(--radius-full);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 4rem;
    position: relative;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-item:nth-child(even) .timeline-content {
    text-align: right;
}

.timeline-year {
    width: 120px;
    height: 120px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--white);
    position: relative;
    z-index: 2;
    box-shadow: var(--shadow-xl);
}

.timeline-content {
    flex: 1;
    padding: 0 3rem;
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-2xl);
}

.timeline-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.timeline-content p {
    color: var(--gray-600);
    line-height: 1.7;
}

/* About Values */
.about-values {
    margin-bottom: 6rem;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.value-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.05), transparent);
    transition: left 0.8s ease;
}

.value-card:hover::before {
    left: 100%;
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-color);
}

.value-icon {
    width: 100px;
    height: 100px;
    background: var(--gradient-primary);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-bounce);
}

.value-card:hover .value-icon {
    transform: scale(1.1) rotate(5deg);
}

.value-icon i {
    font-size: 2.5rem;
    color: var(--white);
}

.value-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.value-card p {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.value-metrics {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.metric {
    background: var(--gray-100);
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.85rem;
    font-weight: 600;
}

/* About Manifesto */
.about-manifesto {
    background: var(--gradient-dark);
    border-radius: var(--radius-2xl);
    padding: 4rem;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.about-manifesto::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
}

.manifesto-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.manifesto-text h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 2rem;
}

.manifesto-text blockquote {
    font-size: 1.2rem;
    line-height: 1.8;
    font-style: italic;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    position: relative;
    padding-left: 2rem;
}

.manifesto-text blockquote::before {
    content: '"';
    position: absolute;
    left: 0;
    top: -10px;
    font-size: 4rem;
    color: var(--accent-color);
    font-family: var(--font-heading);
}

.manifesto-signature {
    text-align: left;
}

.manifesto-signature strong {
    display: block;
    font-size: 1.1rem;
    color: var(--accent-color);
    margin-bottom: 0.25rem;
}

.manifesto-signature span {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.manifesto-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.vision-circle {
    position: relative;
    width: 200px;
    height: 200px;
}

.circle-layer {
    position: absolute;
    border-radius: var(--radius-full);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.layer-1 {
    width: 200px;
    height: 200px;
    animation: rotate 20s linear infinite;
}

.layer-2 {
    width: 150px;
    height: 150px;
    top: 25px;
    left: 25px;
    animation: rotate 15s linear infinite reverse;
}

.layer-3 {
    width: 100px;
    height: 100px;
    top: 50px;
    left: 50px;
    animation: rotate 10s linear infinite;
}

.circle-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
}

.circle-center i {
    font-size: 1.5rem;
    color: var(--white);
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* ===== PHILOSOPHY SECTION ===== */
.philosophy-section {
    background: var(--white);
    position: relative;
    overflow: hidden;
}

.philosophy-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.philosophy-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
    animation: philosophy-float 25s ease-in-out infinite;
}

@keyframes philosophy-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(20px) rotate(-1deg); }
}

.philosophy-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 6rem;
    align-items: start;
    margin-bottom: 6rem;
}

.philosophy-principles {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.principle-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.principle-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
    transform: scaleY(0);
    transition: transform var(--transition-normal);
}

.principle-card:hover::before {
    transform: scaleY(1);
}

.principle-card:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-2xl);
}

.principle-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.principle-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-color);
    font-family: var(--font-heading);
    line-height: 1;
}

.principle-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-bounce);
}

.principle-card:hover .principle-icon {
    transform: scale(1.1) rotate(10deg);
}

.principle-icon i {
    font-size: 2rem;
    color: var(--white);
}

.principle-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.principle-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--gray-600);
    margin-bottom: 2rem;
}

.principle-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--gray-600);
    font-size: 0.95rem;
}

.detail-item i {
    color: var(--primary-color);
    font-size: 1rem;
}

/* Philosophy Model */
.philosophy-model {
    position: sticky;
    top: 2rem;
}

.model-container {
    background: var(--gradient-dark);
    border-radius: var(--radius-2xl);
    padding: 3rem;
    text-align: center;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.model-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,2 18,7 18,13 10,18 2,13 2,7" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
}

.model-container h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

.model-diagram {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto 2rem;
}

.model-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.center-circle {
    width: 100px;
    height: 100px;
    background: var(--gradient-accent);
    border-radius: var(--radius-full);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-glow-accent);
    animation: pulse-glow 3s ease-in-out infinite;
}

.center-circle i {
    font-size: 2rem;
    color: var(--white);
    margin-bottom: 0.25rem;
}

.center-circle span {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--white);
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: var(--shadow-glow-accent);
    }
    50% {
        box-shadow: 0 0 40px rgba(245, 158, 11, 0.6);
    }
}

.model-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.model-element {
    position: absolute;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-bounce);
    cursor: pointer;
}

.model-element:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.element-1 { top: 0; left: 50%; transform: translateX(-50%); }
.element-2 { top: 50%; right: 0; transform: translateY(-50%); }
.element-3 { bottom: 0; left: 50%; transform: translateX(-50%); }
.element-4 { top: 50%; left: 0; transform: translateY(-50%); }

.element-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.element-icon i {
    font-size: 1rem;
    color: var(--white);
}

.model-element span {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--white);
    text-align: center;
}

.model-connections {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.connection-circle {
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 2;
    fill: none;
    stroke-dasharray: 10, 5;
    animation: rotate-connection 20s linear infinite;
}

.connection-path {
    stroke: var(--accent-color);
    stroke-width: 2;
    fill: none;
    opacity: 0.6;
    stroke-dasharray: 5, 5;
    animation: dash-flow 3s linear infinite;
}

@keyframes rotate-connection {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes dash-flow {
    to { stroke-dashoffset: -20; }
}

.model-description {
    position: relative;
    z-index: 1;
}

.model-description p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
}

/* Philosophy Impact */
.philosophy-impact {
    background: var(--gray-50);
    border-radius: var(--radius-2xl);
    padding: 4rem;
}

.impact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.impact-stat {
    text-align: center;
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.impact-stat:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.impact-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.impact-icon i {
    font-size: 2rem;
    color: var(--white);
}

.impact-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-color);
    font-family: var(--font-heading);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.impact-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.impact-description {
    font-size: 0.9rem;
    color: var(--gray-600);
    line-height: 1.5;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .hero-main {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .manifesto-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .philosophy-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .model-container {
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    :root {
        --container-padding: 1rem;
        --section-padding: 80px 0;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 2rem 0;
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-2xl);
        overflow-y: auto;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu .nav-item {
        margin: 0.5rem 0;
        width: 90%;
    }

    .nav-link {
        width: 100%;
        justify-content: center;
        padding: 1rem 2rem;
        font-size: 1.1rem;
    }

    .hamburger {
        display: flex;
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3.5rem);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-large {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 2rem;
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .story-timeline::before {
        left: 2rem;
    }

    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
        padding-left: 4rem;
    }

    .timeline-item:nth-child(even) {
        flex-direction: column;
        align-items: flex-start;
    }

    .timeline-item:nth-child(even) .timeline-content {
        text-align: left;
    }

    .timeline-year {
        position: absolute;
        left: -2rem;
        width: 80px;
        height: 80px;
        font-size: 1.2rem;
    }

    .timeline-content {
        padding: 2rem;
        margin-left: 0;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .philosophy-principles {
        gap: 2rem;
    }

    .principle-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .principle-number {
        font-size: 2rem;
    }

    .principle-icon {
        width: 60px;
        height: 60px;
    }

    .model-diagram {
        width: 250px;
        height: 250px;
    }

    .gen-circle {
        width: 80px;
        height: 80px;
    }

    .circle-content i {
        font-size: 1.5rem;
    }

    .circle-content span {
        font-size: 0.8rem;
    }

    .circle-content small {
        font-size: 0.6rem;
    }

    .impact-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    :root {
        --container-padding: 0.75rem;
        --section-padding: 60px 0;
    }

    .hero-content {
        padding: 100px 0 60px;
    }

    .hero-badge {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .subtitle-highlights {
        gap: 0.5rem;
    }

    .highlight-item {
        font-size: 0.9rem;
    }

    .trust-logos {
        gap: 1rem;
    }

    .trust-logo {
        padding: 0.4rem 0.8rem;
        font-size: 0.75rem;
    }

    .section-badge {
        padding: 0.4rem 1rem;
        font-size: 0.8rem;
    }

    .section-title {
        font-size: clamp(1.8rem, 6vw, 2.5rem);
    }

    .section-subtitle {
        font-size: 1rem;
    }

    .about-manifesto {
        padding: 2.5rem;
    }

    .manifesto-text h3 {
        font-size: 1.5rem;
    }

    .manifesto-text blockquote {
        font-size: 1rem;
        padding-left: 1.5rem;
    }

    .vision-circle {
        width: 150px;
        height: 150px;
    }

    .layer-1 { width: 150px; height: 150px; }
    .layer-2 { width: 110px; height: 110px; top: 20px; left: 20px; }
    .layer-3 { width: 70px; height: 70px; top: 40px; left: 40px; }

    .circle-center {
        width: 40px;
        height: 40px;
    }

    .circle-center i {
        font-size: 1rem;
    }

    .value-card,
    .principle-card {
        padding: 2rem;
    }

    .value-icon,
    .principle-icon {
        width: 60px;
        height: 60px;
    }

    .value-icon i,
    .principle-icon i {
        font-size: 1.5rem;
    }

    .model-container {
        padding: 2rem;
    }

    .model-diagram {
        width: 200px;
        height: 200px;
    }

    .model-element {
        width: 60px;
        height: 60px;
    }

    .element-icon {
        width: 30px;
        height: 30px;
    }

    .element-icon i {
        font-size: 0.8rem;
    }

    .model-element span {
        font-size: 0.6rem;
    }

    .center-circle {
        width: 70px;
        height: 70px;
    }

    .center-circle i {
        font-size: 1.5rem;
    }

    .center-circle span {
        font-size: 0.7rem;
    }
}

/* ===== ACCESSIBILITY ===== */
.keyboard-navigation *:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-shape {
        animation: none;
    }

    .hero-particles,
    .philosophy-particles {
        animation: none;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .floating-elements,
    .hero-scroll-indicator,
    .scroll-progress {
        display: none;
    }

    .hero-section {
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-background {
        display: none;
    }

    section {
        padding: 1rem 0;
        break-inside: avoid;
    }

    .container {
        max-width: 100%;
        padding: 0;
    }
}

/* ===== DARK THEME SUPPORT ===== */
.dark-theme {
    --white: #1a1a1a;
    --gray-50: #2a2a2a;
    --gray-100: #3a3a3a;
    --gray-200: #4a4a4a;
    --dark-color: #ffffff;
    --gray-700: #e0e0e0;
    --gray-600: #d0d0d0;
}

.dark-theme .navbar {
    background: rgba(26, 26, 26, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .section-badge {
    background: var(--gradient-primary);
}

.dark-theme .about-section {
    background: var(--gray-50);
}

.dark-theme .philosophy-section {
    background: var(--white);
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    font-family: var(--font-heading);
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--dark-color);
}

.nav-logo .accent {
    color: var(--accent-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    font-size: 0.95rem;
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger .bar {
    width: 25px;
    height: 3px;
    background: var(--gray-700);
    transition: all var(--transition-normal);
}

.hamburger.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active .bar:nth-child(2) {
    opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--dark-color) 100%);
    z-index: -2;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
    width: 100%;
    padding: var(--section-padding);
}

.hero-text {
    text-align: center;
    color: var(--white);
    margin-bottom: 4rem;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateY(30px);
    animation: slideInUp 0.8s ease forwards;
}

.title-line:nth-child(2) {
    animation-delay: 0.2s;
}

.title-line:nth-child(3) {
    animation-delay: 0.4s;
}

.title-line.accent {
    color: var(--accent-color);
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 3rem;
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.8s ease 0.6s forwards;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.8s ease 0.8s forwards;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--accent-color);
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    background: var(--accent-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.8s ease 1s forwards;
}

.stat-item {
    text-align: center;
    color: var(--white);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    font-family: var(--font-heading);
    color: var(--accent-color);
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    animation: fadeIn 1s ease 1.5s forwards;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid var(--white);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
    animation: bounce 2s infinite;
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) rotate(45deg);
    }
    40% {
        transform: translateY(-10px) rotate(45deg);
    }
    60% {
        transform: translateY(-5px) rotate(45deg);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* ===== SECTION STYLES ===== */
section {
    padding: var(--section-padding);
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-heading);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* ===== ABOUT SECTION ===== */
.about {
    background: var(--gray-100);
}

.about-content {
    max-width: 1000px;
    margin: 0 auto;
}

.about-text {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.about-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.about-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.05), transparent);
    transition: left 0.6s ease;
}

.about-card:hover::before {
    left: 100%;
}

.about-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all var(--transition-normal);
}

.about-card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
}

.card-icon i {
    font-size: 2rem;
    color: var(--white);
}

.about-card h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.about-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* ===== SERVICES SECTION ===== */
.services {
    background: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.service-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all var(--transition-normal);
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(10deg);
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
}

.service-icon i {
    font-size: 2.5rem;
    color: var(--white);
}

.service-card h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--gray-600);
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.service-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all var(--transition-normal);
}

.service-btn:hover {
    background: var(--primary-dark);
    transform: translateX(5px);
}

/* ===== SOLUTIONS SECTION ===== */
.solutions {
    background: var(--gray-100);
}

.solutions-content {
    max-width: 1000px;
    margin: 0 auto;
}

.solution-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 1rem 2rem;
    background: var(--white);
    border: 2px solid var(--gray-300);
    border-radius: 25px;
    font-weight: 500;
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.tab-btn:hover,
.tab-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.tab-content {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.tab-content.active {
    display: block;
}

.solution-detail {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
}

.solution-text h3 {
    font-family: var(--font-heading);
    font-size: 2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
}

.solution-text p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.solution-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: var(--gray-100);
    border-radius: 10px;
    transition: all var(--transition-normal);
}

.benefit-item:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateX(5px);
}

.benefit-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.benefit-item:hover i {
    color: var(--white);
}

.generation-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
}

.stat-circle {
    text-align: center;
}

.circle-progress {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--gray-200) 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    position: relative;
}

.circle-progress::before {
    content: '';
    position: absolute;
    width: 90px;
    height: 90px;
    background: var(--white);
    border-radius: 50%;
}

.percentage {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    position: relative;
    z-index: 1;
}

.stat-circle p {
    color: var(--gray-600);
    font-weight: 500;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESOURCES SECTION ===== */
.resources {
    background: var(--white);
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.resource-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 15px;
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.resource-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.resource-image {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.resource-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.resource-image i {
    font-size: 4rem;
    color: var(--white);
    position: relative;
    z-index: 1;
}

.resource-content {
    padding: 2rem;
}

.resource-content h3 {
    font-family: var(--font-heading);
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.resource-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.resource-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.resource-type,
.resource-pages,
.resource-time,
.resource-duration,
.resource-read {
    background: var(--gray-100);
    color: var(--gray-600);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}

.resource-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all var(--transition-normal);
}

.resource-btn:hover {
    background: var(--primary-dark);
    transform: translateX(5px);
}

/* ===== TEAM SECTION ===== */
.team {
    background: var(--gray-100);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.team-member {
    background: var(--white);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.team-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.05), transparent);
    transition: left 0.6s ease;
}

.team-member:hover::before {
    left: 100%;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.member-image {
    position: relative;
    margin-bottom: 2rem;
}

.member-avatar {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    transition: all var(--transition-normal);
}

.team-member:hover .member-avatar {
    transform: scale(1.1);
}

.member-avatar i {
    font-size: 3rem;
    color: var(--white);
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(37, 99, 235, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-normal);
}

.team-member:hover .member-overlay {
    opacity: 1;
}

.member-social {
    display: flex;
    gap: 1rem;
}

.member-social a {
    width: 40px;
    height: 40px;
    background: var(--white);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.member-social a:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: scale(1.1);
}

.member-info h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.member-role {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 1rem;
}

.member-bio {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.member-expertise {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.expertise-tag {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* ===== CONTACT SECTION ===== */
.contact {
    background: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1000px;
    margin: 0 auto;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    background: var(--gray-100);
    border-radius: 15px;
    transition: all var(--transition-normal);
}

.contact-item:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateX(10px);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all var(--transition-normal);
}

.contact-item:hover .contact-icon {
    background: var(--white);
}

.contact-icon i {
    font-size: 1.2rem;
    color: var(--white);
    transition: color var(--transition-normal);
}

.contact-item:hover .contact-icon i {
    color: var(--primary-color);
}

.contact-details h3 {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    transition: color var(--transition-normal);
}

.contact-item:hover .contact-details h3 {
    color: var(--white);
}

.contact-details p {
    color: var(--gray-600);
    line-height: 1.6;
    transition: color var(--transition-normal);
}

.contact-item:hover .contact-details p {
    color: var(--white);
}

.contact-form {
    background: var(--gray-100);
    padding: 3rem;
    border-radius: 20px;
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--gray-300);
    border-radius: 10px;
    font-size: 1rem;
    font-family: var(--font-primary);
    background: var(--white);
    transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group label {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: var(--gray-500);
    font-size: 1rem;
    pointer-events: none;
    transition: all var(--transition-normal);
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group select:focus + label,
.form-group select:not([value=""]) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label {
    top: -0.5rem;
    left: 0.8rem;
    font-size: 0.85rem;
    color: var(--primary-color);
    background: var(--white);
    padding: 0 0.5rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--white);
}

.footer-section h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.footer-section p {
    color: var(--gray-400);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-section ul li a:hover {
    color: var(--accent-color);
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    width: 45px;
    height: 45px;
    background: var(--gray-700);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.footer-social a:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    color: var(--gray-400);
    margin: 0;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-links a:hover {
    color: var(--accent-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-link {
        font-size: 1.2rem;
    }

    .hamburger {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .solution-detail {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }

    section {
        padding: 60px 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .services-grid,
    .resources-grid,
    .team-grid {
        grid-template-columns: 1fr;
    }

    .about-text {
        grid-template-columns: 1fr;
    }

    .solution-tabs {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 100%;
        max-width: 250px;
    }

    .contact-form {
        padding: 2rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* ===== SCROLL ANIMATIONS ===== */
@media (prefers-reduced-motion: no-preference) {
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }

    .animate-on-scroll.animated {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RIPPLE EFFECT ===== */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* ===== ADDITIONAL INTERACTIVE EFFECTS ===== */
.btn,
.service-btn,
.resource-btn {
    position: relative;
    overflow: hidden;
}

/* Glowing effect for primary buttons */
.btn-primary {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.btn-primary:hover {
    box-shadow: 0 0 30px rgba(245, 158, 11, 0.5);
}

/* Pulse animation for important elements */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Floating animation for decorative elements */
@keyframes floating {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.floating {
    animation: floating 3s ease-in-out infinite;
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Hover glow effect for cards */
.glow-on-hover {
    transition: all var(--transition-normal);
}

.glow-on-hover:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* Loading skeleton animation */
@keyframes skeleton-loading {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-200);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Selection styling */
::selection {
    background: var(--primary-color);
    color: var(--white);
}

::-moz-selection {
    background: var(--primary-color);
    color: var(--white);
}